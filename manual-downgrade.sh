#!/bin/bash

# Alternativa manual: conectar diretamente no banco e fazer downgrade

echo "=== Backup do banco ==="
kubectl exec -n airflow deployment/airflow-postgresql -- pg_dump -U postgres airflow > airflow_backup_$(date +%Y%m%d_%H%M%S).sql

echo "=== Verificando revisão atual do banco ==="
kubectl exec -n airflow deployment/airflow-postgresql -- psql -U postgres -d airflow -c "SELECT version_num FROM alembic_version;"

echo "=== Fazendo downgrade manual para revisão compatível com 2.8.1 ==="
kubectl exec -n airflow deployment/airflow-postgresql -- psql -U postgres -d airflow -c "UPDATE alembic_version SET version_num = '7b2661a43ba3';"

echo "=== Verificando se o downgrade foi aplicado ==="
kubectl exec -n airflow deployment/airflow-postgresql -- psql -U postgres -d airflow -c "SELECT version_num FROM alembic_version;"

echo "=== Agora pode rebuildar a imagem e reinstalar o Airflow 2.8.1 ==="