paulo@BQCON-N0101:~/airflow$ k logs airflow-run-airflow-migrations-jsmrs -n airflow
 
[2025-07-29T13:37:55.415+0000] {configuration.py:1194} WARNING - No module named 'airflow.providers.fab'
[2025-07-29T13:37:55.416+0000] {cli_parser.py:77} WARNING - cannot load CLI commands from auth manager: The object could not be loaded. Please check "auth_manager" key in "core" section. Current value: "airflow.providers.fab.auth_manager.fab_auth_manager.FabAuthManager".
[2025-07-29T13:37:55.416+0000] {cli_parser.py:78} WARNING - Authentication manager is not configured and webserver will not be able to start.
DB: **********************************************/airflow?sslmode=disable
Performing upgrade to the metadata database **********************************************/airflow?sslmode=disable
[2025-07-29T13:37:56.940+0000] {migration.py:216} INFO - Context impl PostgresqlImpl.
[2025-07-29T13:37:56.941+0000] {migration.py:219} INFO - Will assume transactional DDL.
[2025-07-29T13:37:56.949+0000] {db.py:1616} INFO - Creating tables
INFO  [alembic.runtime.migration] Context impl PostgresqlImpl.
INFO  [alembic.runtime.migration] Will assume transactional DDL.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/base.py", line 251, in _catch_revision_errors
    yield
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/base.py", line 459, in _upgrade_revs
    for script in reversed(list(revs))
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 813, in iterate_revisions
    revisions, heads = fn(
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 1466, in _collect_upgrade_revisions
    current_revisions = self.get_revisions(lower)
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 543, in get_revisions
    return sum([self.get_revisions(id_elem) for id_elem in id_], ())
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 543, in <listcomp>
    return sum([self.get_revisions(id_elem) for id_elem in id_], ())
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 566, in get_revisions
    return tuple(
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 567, in <genexpr>
    self._revision_for_ident(rev_id, branch_label)
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/revision.py", line 638, in _revision_for_ident
    raise ResolutionError(
alembic.script.revision.ResolutionError: No such revision or branch '1949afb29106'
 
The above exception was the direct cause of the following exception:
 
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/airflow", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/__main__.py", line 57, in main
    args.func(args)
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/cli/cli_config.py", line 49, in command
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/utils/cli.py", line 114, in wrapper
    return f(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/utils/providers_configuration_loader.py", line 55, in wrapped_function
    return func(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/cli/commands/db_command.py", line 129, in migratedb
    db.upgradedb(
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/utils/session.py", line 79, in wrapper
    return func(*args, session=session, **kwargs)
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/utils/db.py", line 1623, in upgradedb
    command.upgrade(config, revision=to_revision or "heads")
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/command.py", line 403, in upgrade
    script.run_env()
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/base.py", line 583, in run_env
    util.load_python_file(self.dir, "env.py")
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/util/pyfiles.py", line 95, in load_python_file
    module = load_module_py(module_id, path)
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/util/pyfiles.py", line 113, in load_module_py
    spec.loader.exec_module(module)  # type: ignore
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/migrations/env.py", line 117, in <module>
    run_migrations_online()
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/migrations/env.py", line 111, in run_migrations_online
    context.run_migrations()
  File "<string>", line 8, in run_migrations
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/runtime/environment.py", line 948, in run_migrations
    self.get_context().run_migrations(**kw)
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/runtime/migration.py", line 615, in run_migrations
    for step in self._migrations_fn(heads, self):
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/command.py", line 392, in upgrade
    return script._upgrade_revs(revision, rev)
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/base.py", line 455, in _upgrade_revs
    return [
  File "/usr/local/lib/python3.8/contextlib.py", line 131, in __exit__
    self.gen.throw(type, value, traceback)
  File "/home/<USER>/.local/lib/python3.8/site-packages/alembic/script/base.py", line 283, in _catch_revision_errors
    raise util.CommandError(resolution) from re
alembic.util.exc.CommandError: Can't locate revision identified by '1949afb29106'