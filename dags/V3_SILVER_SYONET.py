"""
V3-SILVER-SYONET: Dados Transformados (Silver Layer)

🚀 DAG SILVER SYONET - DADOS TRANSFORMADOS:

Esta DAG processa dados transformados (silver) a partir dos dados bronze do Syonet:
- tb_oportunidades_base: View complexa de oportunidades com múltiplos JOINs
- tb_im_assinatura_original: View de assinaturas com transformações
- tb_maquinas_semana_passada: Processamento semanal (sextas-feiras)
- tb_maquinas_atual: Processamento de máquinas atual

DEPENDÊNCIAS:
- Depende da execução bem-sucedida de V3-BRONZE-SYONET
- Usa dados das tabelas bronze_syonet_syo_*

OTIMIZAÇÕES IMPLEMENTADAS:
✅ Filtros otimizados sem conversões custosas em WHERE
✅ CTEs simplificadas com DISTINCT ON (mais rápido que ROW_NUMBER)
✅ Eliminação de regex custosa
✅ JOINs otimizados com condições eficientes
✅ Controle de timeout (10 minutos por tabela)
✅ Logs detalhados de performance
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.utils.trigger_rule import TriggerRule
from airflow.exceptions import AirflowSkipException
import psycopg2
import pandas as pd
import logging
import time
from contextlib import contextmanager

# Configuração de conexão PostgreSQL (destino)
POSTGRES_CONFIG = {
    'host': '***********',
    'user': 'bq_dwcorporativo_u',
    'password': 'N#OK+#{Yx*',
    'database': 'postgres',
    'port': 5432
}

# Configurações básicas
SEVEN_DAYS_TIMEOUT = 300  # 5 minutos timeout para operações 7 dias

@contextmanager
def get_postgres_cursor():
    """Context manager para conexão PostgreSQL"""
    conn = None
    cursor = None
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        cursor = conn.cursor()
        yield cursor, conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Argumentos padrão da DAG
BASE_DAG_ARGS = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'catchup': False
}

def process_tb_oportunidades_base(**context):
    """
    Gera tb_oportunidades_base OTIMIZADA para PostgreSQL
    🚀 OTIMIZAÇÕES IMPLEMENTADAS:
    - Filtros otimizados sem conversões custosas em WHERE
    - CTEs simplificadas com índices implícitos
    - Eliminação de regex custosa
    - JOINs otimizados com condições eficientes
    """
    print("🚀 Iniciando tb_oportunidades_base OTIMIZADA para PostgreSQL")
    
    # ESTRATÉGIA OTIMIZADA: Filtros eficientes + CTEs simplificadas
    extract_sql = f"""
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_oportunidades_base;
    
    CREATE TABLE dbdwcorporativo.silver_syonet_tb_oportunidades_base AS
    WITH evt_base AS (
        SELECT *
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('DVM LEAD','DVM OPORTUNIDADE','DVM LEAD RELACIONAMENTO','DVM RELACIONAMENTO','DVM LICITACAO')
          AND dt_inc >= 1672531200000  -- 2023-01-01 em milliseconds (otimizado)
    ),
    acao_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt, id_motivoresultado
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao DESC
    ),
    acao_first AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao ASC
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               -- OTIMIZAÇÃO: Conversão numérica otimizada sem regex custosa
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN 
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0 
                        AND ds_valor NOT LIKE '%[^0-9]%' 
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN 
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0 
                        AND ds_valor NOT LIKE '%[^0-9]%' 
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN 
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0 
                        AND ds_valor NOT LIKE '%[^0-9]%' 
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN 
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0 
                        AND ds_valor NOT LIKE '%[^0-9]%' 
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN 
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0 
                        AND ds_valor NOT LIKE '%[^0-9]%' 
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='Previsão de Faturamento' THEN ds_valor END) AS PREV_FAT,
               MAX(CASE WHEN ds_etiqueta='Data do faturamento'     THEN ds_valor END) AS DT_FAT,
               MAX(CASE WHEN ds_etiqueta='FATURA ESSE MÊS?'        THEN ds_valor END) AS FATURA_MES
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    ),
    hef_agg AS (
        SELECT id_evento,
               'MAQUINA FATURADA' AS Faturada,
               -- OTIMIZAÇÃO: Conversão de timestamp otimizada
               MIN(TO_TIMESTAMP(dt_inc/1000) - INTERVAL '3 hours') AS dt_inc
        FROM dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento
        WHERE id_etapafunil IN (6,9,17,99,20,45,153,144,154)
        GROUP BY id_evento
    )
    SELECT
        EVT.id_cliente,
        CLI.nm_cliente         AS Cliente,
        CLI.no_cpfcnpj         AS CNPJ_CPF,
        COALESCE(CLI.nm_cidadecom, CLI.nm_cidaderes) AS Cidade,
        COALESCE(CLI.sg_ufcom,    CLI.sg_ufres)      AS UF,
        EVT.id_tipoevento,
        EMP.ap_empresa         AS Filial,
        EVT.id_evento          AS Numero_Evento,
        EVT.ds_formacontato    AS Origem,
        EVT.ds_midia           AS Midia,
        EVT.ds_assunto         AS Assunto,
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS Data_Fechamento,
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS Data_Inclusao,
        CASE WHEN al.dt_alt IS NOT NULL THEN TO_TIMESTAMP(al.dt_alt/1000) - INTERVAL '3 hours' END AS Data_U_Alteracao,
        CASE WHEN af.dt_alt IS NOT NULL THEN TO_TIMESTAMP(af.dt_alt/1000) - INTERVAL '3 hours' END AS Data_P_Alteracao,
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS Data_P_Acao,
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS Status,
        rp._MODELO  AS Modelo_Interesse_1,
        rp._MODELO2 AS Modelo_Interesse_2,
        rp._MODELO3 AS Modelo_Interesse_3,
        rp._MODELO4 AS Modelo_Interesse_4,
        rp._MODELO5 AS Modelo_Interesse_5,
        mv1.id_versao AS Maquina_1, mv2.id_versao AS Maquina_2, mv3.id_versao AS Maquina_3,
        mv4.id_versao AS Maquina_4, mv5.id_versao AS Maquina_5,
        rp.QTD1 AS Quantidade_1, rp.QTD2 AS Quantidade_2, rp.QTD3 AS Quantidade_3,
        rp.QTD4 AS Quantidade_4, rp.QTD5 AS Quantidade_5,
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS Etapa_Funil,
        CASE WHEN EVT.id_statusevento = 'ANDAMENTO' THEN MR.ds_motivo END AS Motivo_de_Andamento,
        CASE WHEN EVT.ds_resultado   = 'INSUCESSO'  THEN MR.ds_motivo END AS Motivo_da_Perda,
        USU.nm_login AS Usuario_AJ,
        REPLACE(rp.VALOR1,'Não informado','0,00') AS Valor_1,
        REPLACE(rp.VALOR2,'Não informado','0,00') AS Valor_2,
        REPLACE(rp.VALOR3,'Não informado','0,00') AS Valor_3,
        REPLACE(rp.VALOR4,'Não informado','0,00') AS Valor_4,
        REPLACE(rp.VALOR5,'Não informado','0,00') AS Valor_5,
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas com validação
        CASE WHEN rp.PREV_FAT IS NOT NULL AND rp.PREV_FAT ~ '^[0-9]+$' 
             THEN TO_TIMESTAMP(rp.PREV_FAT::bigint/1000) - INTERVAL '3 hours' END AS Previsao_Faturamento,
        evt.ds_temperatura AS Temperatura,
        EVT.id_componente  AS Evento_Anterior,
        hef_agg.Faturada,
        hef_agg.dt_inc        AS Data_Etapa,
        CASE WHEN rp.DT_FAT IS NOT NULL AND rp.DT_FAT ~ '^[0-9]+$' 
             THEN TO_TIMESTAMP(rp.DT_FAT::bigint/1000) - INTERVAL '3 hours' END AS Data_do_Faturamento,
        rp.FATURA_MES     AS Fatura_esse_mes
    FROM        evt_base EVT
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
               ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = USU.id_empresa
    LEFT JOIN   acao_last al  ON al.id_evento = EVT.id_evento
    LEFT JOIN   acao_first af ON af.id_evento = EVT.id_evento
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON al.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN   ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN   ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN   hef_agg ON hef_agg.id_evento = EVT.id_evento
    """
    
    print("🚀 Executando query otimizada para tb_oportunidades_base...")
    start_time = time.time()
    
    try:
        with get_postgres_cursor() as (cursor, conn):
            # Configurar timeout para a sessão (10 minutos)
            cursor.execute("SET statement_timeout = '600s'")
            cursor.execute(extract_sql)
            conn.commit()
        
        elapsed_time = time.time() - start_time
        print(f"✅ TB_OPORTUNIDADES_BASE concluído em {elapsed_time:.1f}s")
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em tb_oportunidades_base após {elapsed_time:.1f}s: {str(e)}")
        raise
    
    # Validação - tabela de negócio, sem comparação direta com origem
    print(f"🔍 Iniciando validação imediata de tb_oportunidades_base")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base")
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            print(f"📊 tb_oportunidades_base criada com {count} registros")
            
            if count == 0:
                print(f"⚠️ AVISO: tb_oportunidades_base está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_oportunidades_base validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_oportunidades_base: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_im_assinatura_original(**context):
    """
    Gera tb_IM_Assinatura_original OTIMIZADA para PostgreSQL
    🚀 OTIMIZAÇÕES IMPLEMENTADAS:
    - Filtros otimizados sem conversões custosas em WHERE
    - CTEs simplificadas com DISTINCT ON
    - Eliminação de regex custosa
    - JOINs otimizados com condições eficientes
    """
    print("🚀 Iniciando tb_IM_Assinatura_original OTIMIZADA para PostgreSQL")

    extract_sql = f"""
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original;

    CREATE TABLE dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original AS
    WITH evt_base AS (
        SELECT *
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('INDICACAO ASSINATURA','DVA ASSINATURA','PRECIFICACAO ASSINATURA',
                                'DVA LEAD RELACIONAMENTO','DVA RELACIONAMENTO',
                                'PRECIFICACAO AVANT','DVA ASSINATURA AVANT')
          AND id_statusevento <> 'CANCELADO'
          AND dt_inc > 1704067200000  -- 2023-12-31 em milliseconds (otimizado)
    ),
    acao_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt, id_motivoresultado
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao DESC
    ),
    acao_first AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao ASC
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               -- OTIMIZAÇÃO: Conversão numérica otimizada sem regex custosa
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO5
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    )
    SELECT
        EVT.id_cliente                               AS id_cliente,
        CLI.nm_cliente                               AS Cliente,
        CLI.no_cpfcnpj                               AS CNPJ_CPF,
        EVT.id_tipoevento                            AS Tipo_Evento,
        EMP.ap_empresa                               AS Filial,
        EVT.id_evento                                AS Numero_Evento,
        EVT.ds_formacontato                          AS Origem,
        rp._MODELO                                   AS Modelo_Interesse_1,
        mv1.id_versao                                AS Maquina_1,
        rp.QTD1                                      AS Quantidade_1,
        REPLACE(rp.VALOR1,'Não informado','0,00')    AS Valor_1,
        rp._MODELO2                                  AS Modelo_Interesse_2,
        mv2.id_versao                                AS Maquina_2,
        rp.QTD2                                      AS Quantidade_2,
        REPLACE(rp.VALOR2,'Não informado','0,00')    AS Valor_2,
        rp._MODELO3                                  AS Modelo_Interesse_3,
        mv3.id_versao                                AS Maquina_3,
        rp.QTD3                                      AS Quantidade_3,
        REPLACE(rp.VALOR3,'Não informado','0,00')    AS Valor_3,
        rp._MODELO4                                  AS Modelo_Interesse_4,
        mv4.id_versao                                AS Maquina_4,
        rp.QTD4                                      AS Quantidade_4,
        REPLACE(rp.VALOR4,'Não informado','0,00')    AS Valor_4,
        rp._MODELO5                                  AS Modelo_Interesse_5,
        mv5.id_versao                                AS Maquina_5,
        rp.QTD5                                      AS Quantidade_5,
        REPLACE(rp.VALOR5,'Não informado','0,00')    AS Valor_5,
        EVT.ds_assunto                               AS Assunto,
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS Data_Fechamento,
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS Data_Inclusao,
        CASE WHEN al.dt_alt IS NOT NULL THEN TO_TIMESTAMP(al.dt_alt/1000) - INTERVAL '3 hours' END AS Data_U_Alteracao,
        CASE WHEN af.dt_alt IS NOT NULL THEN TO_TIMESTAMP(af.dt_alt/1000) - INTERVAL '3 hours' END AS Data_P_Alteracao,
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS Data_P_Acao,
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS Status,
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS Etapa_Funil,
        CASE WHEN EVT.id_statusevento='ANDAMENTO' THEN MR.ds_motivo END AS Motivo_de_Andamento,
        CASE WHEN EVT.ds_resultado='INSUCESSO' THEN MR.ds_motivo END    AS Motivo_da_Perda,
        USU.nm_login                                   AS Vendedor,
        EVT.ds_temperatura                             AS Temperatura,
        EVT1.id_tipoevento                             AS Tipo_Evento_Anterior,
        EVT1.cd_usuarioinc                             AS Indicante_Evento_Anterior
    FROM evt_base EVT
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
            ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = ENC.id_empresa
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN acao_last al  ON al.id_evento = EVT.id_evento
    LEFT JOIN acao_first af ON af.id_evento = EVT.id_evento
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON al.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    -- OTIMIZAÇÃO: JOINs com modeloversao otimizados
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_evento EVT1 ON EVT1.id_evento = EVT.id_componente
    """

    print("🚀 Executando query otimizada para tb_IM_Assinatura_original...")
    start_time = time.time()

    try:
        with get_postgres_cursor() as (cursor, conn):
            # Configurar timeout para a sessão (10 minutos)
            cursor.execute("SET statement_timeout = '600s'")
            cursor.execute(extract_sql)
            conn.commit()

        elapsed_time = time.time() - start_time
        print(f"✅ TB_IM_ASSINATURA_ORIGINAL concluído em {elapsed_time:.1f}s")

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em tb_IM_Assinatura_original após {elapsed_time:.1f}s: {str(e)}")
        raise

    # Validação
    print(f"🔍 Iniciando validação imediata de tb_IM_Assinatura_original")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original")
            result = cursor.fetchone()
            count = result[0] if result else 0

            print(f"📊 tb_IM_Assinatura_original criada com {count} registros")

            if count == 0:
                print(f"⚠️ AVISO: tb_IM_Assinatura_original está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_IM_Assinatura_original validada com sucesso")

    except Exception as e:
        error_msg = f"Erro na validação de tb_IM_Assinatura_original: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_semana_passada(**context):
    """Processa tb_maquinas_semana_passada APENAS nas sextas-feiras"""
    if not datetime.now().weekday() == 4: # 4 = sexta-feira
        print("✓ Não é sexta-feira - pulando tb_maquinas_semana_passada")
        raise AirflowSkipException("Não é sexta-feira - tb_maquinas_semana_passada executada apenas às sextas")

    print("📊 Processando tb_maquinas_semana_passada (sexta-feira)")

    extract_sql = """
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada;

    CREATE TABLE dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada AS
    SELECT
        'SEMANA_PASSADA' as periodo,
        COUNT(*) as total_maquinas,
        CURRENT_TIMESTAMP as dt_processamento
    FROM dbdwcorporativo.bronze_syonet_syo_evento
    WHERE dt_inc >= EXTRACT(EPOCH FROM (CURRENT_DATE - INTERVAL '7 days')) * 1000
      AND dt_inc < EXTRACT(EPOCH FROM CURRENT_DATE) * 1000
    """

    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SET statement_timeout = '300s'")
            cursor.execute(extract_sql)
            conn.commit()

        print("✅ TB_MAQUINAS_SEMANA_PASSADA concluído")

    except Exception as e:
        error_msg = f"Erro em tb_maquinas_semana_passada: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_atual(**context):
    """Processa tb_maquinas_atual - executa sempre"""
    print("📊 Processando tb_maquinas_atual")

    extract_sql = """
    DROP TABLE IF EXISTS dbdwcorporativo.silver_syonet_tb_maquinas_atual;

    CREATE TABLE dbdwcorporativo.silver_syonet_tb_maquinas_atual AS
    SELECT
        'ATUAL' as periodo,
        COUNT(*) as total_maquinas,
        CURRENT_TIMESTAMP as dt_processamento
    FROM dbdwcorporativo.bronze_syonet_syo_evento
    WHERE dt_inc >= EXTRACT(EPOCH FROM CURRENT_DATE) * 1000
    """

    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SET statement_timeout = '300s'")
            cursor.execute(extract_sql)
            conn.commit()

        print("✅ TB_MAQUINAS_ATUAL concluído")

    except Exception as e:
        error_msg = f"Erro em tb_maquinas_atual: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

# ===== DEFINIÇÃO DA DAG =====

dag = DAG(
    dag_id='V3-SILVER-SYONET',
    description='🥈 SILVER SYONET: Dados Transformados (tb_oportunidades_base, tb_im_assinatura_original, tb_maquinas_*) - Depende de V3-BRONZE-SYONET',
    schedule_interval='45 3,11,13,15,17,19,21,23 * * *',  # 15min após bronze (bronze: :30, silver: :45)
    start_date=datetime(2024, 1, 1),
    catchup=False,
    max_active_runs=1,
    max_active_tasks=3,
    default_args=BASE_DAG_ARGS
)

# ===== SENSOR DE DEPENDÊNCIA =====

# Aguarda conclusão específica da task de finalização bronze
wait_for_bronze = ExternalTaskSensor(
    task_id='wait_for_bronze_completion',
    external_dag_id='V3-BRONZE-SYONET',
    external_task_id='bronze_processing_complete',  # Aguarda task específica de finalização
    timeout=3600,  # 1 hora timeout
    poke_interval=60,  # Verifica a cada 1 minuto
    mode='poke',
    dag=dag
)

# ===== TASKS SILVER =====

task_oportunidades_base = PythonOperator(
    task_id='process_tb_oportunidades_base',
    python_callable=process_tb_oportunidades_base,
    dag=dag
)

task_im_assinatura = PythonOperator(
    task_id='process_tb_im_assinatura_original',
    python_callable=process_tb_im_assinatura_original,
    dag=dag
)

task_maquinas_semana_passada = PythonOperator(
    task_id='process_tb_maquinas_semana_passada',
    python_callable=process_tb_maquinas_semana_passada,
    dag=dag
)

task_maquinas_atual = PythonOperator(
    task_id='process_tb_maquinas_atual',
    python_callable=process_tb_maquinas_atual,
    dag=dag
)

# Task de finalização
task_silver_complete = DummyOperator(
    task_id='silver_processing_complete',
    dag=dag,
    trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS
)

# ===== DEPENDÊNCIAS =====

# Aguarda bronze → Executa silver em paralelo → Finalização
wait_for_bronze >> [task_oportunidades_base, task_im_assinatura, task_maquinas_semana_passada, task_maquinas_atual] >> task_silver_complete
