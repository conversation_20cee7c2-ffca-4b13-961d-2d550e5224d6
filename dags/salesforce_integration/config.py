"""
ETL Consolidado - Configurações Centralizadas
Todas as configurações de conexões, credenciais e parâmetros do sistema.
"""

import os
from decouple import config
from typing import Dict, Any

# =============================================================================
# CONFIGURAÇÕES DE BANCO DE DADOS
# =============================================================================

DATABASE_CONFIGS = {
    'newcon': {
        'type': 'mssql',
        'primary': {
            'host': config('NEWCON_HOST_BATMAN', default='***********'),
            'port': config('NEWCON_PORT', default=1433, cast=int),
            'database': config('NEWCON_DATABASE', default='dbNewCon'),
            'username': config('NEWCON_USER', default='bq_dwcorporativo_u'),
            'password': config('NEWCON_PASSWORD', default='N#OK+#{Yx*'),
            'driver': config('NEWCON_DRIVER', default='ODBC Driver 17 for SQL Server'),
            'timeout': config('NEWCON_TIMEOUT', default=30, cast=int),
        },
        'secondary': {
            'host': config('NEWCON_HOST_JOCKER', default='***********'),
            'port': config('NEWCON_PORT', default=1433, cast=int),
            'database': config('NEWCON_DATABASE', default='dbNewCon'),
            'username': config('NEWCON_USER', default='bq_dwcorporativo_u'),
            'password': config('NEWCON_PASSWORD', default='N#OK+#{Yx*'),
            'driver': config('NEWCON_DRIVER', default='ODBC Driver 17 for SQL Server'),
            'timeout': config('NEWCON_TIMEOUT', default=30, cast=int),
        },
        'pool_size': config('NEWCON_POOL_SIZE', default=5, cast=int),
        'max_overflow': config('NEWCON_MAX_OVERFLOW', default=10, cast=int),
        'retry_attempts': config('NEWCON_RETRY_ATTEMPTS', default=3, cast=int),
        'retry_delay': config('NEWCON_RETRY_DELAY', default=1, cast=int),
    },
    
    'dw_corporativo': {
        'type': 'postgresql',
        'host': config('DW_HOST', default='***********'),
        'port': config('DW_PORT', default=5432, cast=int),
        'database': config('DW_DATABASE', default='postgres'),
        'username': config('DW_USER', default=''),
        'password': config('DW_PASSWORD', default=''),
        'timeout': config('DW_TIMEOUT', default=30, cast=int),
        'pool_size': config('DW_POOL_SIZE', default=5, cast=int),
        'max_overflow': config('DW_MAX_OVERFLOW', default=10, cast=int),
        'retry_attempts': config('DW_RETRY_ATTEMPTS', default=3, cast=int),
        'retry_delay': config('DW_RETRY_DELAY', default=1, cast=int),
    },
    
    'orbbits': {
        'type': 'mysql',
        'host': config('ORBBITS_HOST', default='************'),
        'port': config('ORBBITS_PORT', default=3306, cast=int),
        'database': config('ORBBITS_DATABASE', default='orbbits_charges'),
        'username': config('ORBBITS_USER', default='bq_dwcorporativo_u'),
        'password': config('ORBBITS_PASSWORD', default='st#FJ3WhTCqB'),
        'timeout': config('ORBBITS_TIMEOUT', default=30, cast=int),
        'pool_size': config('ORBBITS_POOL_SIZE', default=5, cast=int),
        'max_overflow': config('ORBBITS_MAX_OVERFLOW', default=10, cast=int),
        'retry_attempts': config('ORBBITS_RETRY_ATTEMPTS', default=3, cast=int),
        'retry_delay': config('ORBBITS_RETRY_DELAY', default=1, cast=int),
    }
}

# =============================================================================
# CONFIGURAÇÕES SALESFORCE MARKETING CLOUD
# =============================================================================

# ============================================================================
# CONFIGURAÇÃO DE MODO DE OPERAÇÃO - FÁCIL ALTERNÂNCIA
# ============================================================================
#
# 🚀 MODO RÁPIDO (Padrão): Não verifica status dos lotes - Execução rápida
# 🔍 MODO SEGURO: Verifica status de todos os lotes - Execução mais lenta mas confiável
#
# Para alternar entre os modos:
# 1. Mude a variável de ambiente: export SALESFORCE_VERIFY_STATUS=true/false
# 2. Ou mude diretamente no código abaixo (linha OPERATION_MODE)

OPERATION_MODE = {
    'verify_status': config('SALESFORCE_VERIFY_STATUS', default=False, cast=bool),
    'description': 'Modo Seguro (com verificação)' if config('SALESFORCE_VERIFY_STATUS', default=False, cast=bool) else 'Modo Rápido (sem verificação)',
    'recommended_for_production': False,  # True = Modo Seguro, False = Modo Rápido
}

SALESFORCE_CONFIG = {
    'client_id': config('SALESFORCE_CLIENT_ID', default='ljmwlofrhxbmnhdgvyivwpy1'),
    'client_secret': config('SALESFORCE_CLIENT_SECRET', default='N5o7S3i9HXKEz0DWjdnhVn51'),
    'auth_uri': config('SALESFORCE_AUTH_URI', default='https://mc5jxdhk62pllmhs0nc0r--9qgb4.auth.marketingcloudapis.com/'),
    'rest_uri': config('SALESFORCE_REST_URI', default='https://mc5jxdhk62pllmhs0nc0r--9qgb4.rest.marketingcloudapis.com/'),
    'timeout': config('SALESFORCE_TIMEOUT', default=300, cast=int),
    'batch_size': config('SALESFORCE_BATCH_SIZE', default=2000, cast=int),
    'rate_limit': config('SALESFORCE_RATE_LIMIT', default=200, cast=int),  # req/min
    'retry_attempts': config('SALESFORCE_RETRY_ATTEMPTS', default=3, cast=int),
    'retry_delay': config('SALESFORCE_RETRY_DELAY', default=2, cast=int),
    'exponential_backoff': config('SALESFORCE_EXPONENTIAL_BACKOFF', default=True, cast=bool),
    'polling_interval': config('SALESFORCE_POLLING_INTERVAL', default=5, cast=int),
    'max_polling_time': config('SALESFORCE_MAX_POLLING_TIME', default=300, cast=int),
}

# =============================================================================
# CONFIGURAÇÕES RD STATION
# =============================================================================

RDSTATION_CONFIG = {
    'token': config('RDSTATION_TOKEN', default='63dcebb7505962001bdfec12'),
    'base_url': config('RDSTATION_BASE_URL', default='https://crm.rdstation.com/api/v1'),
    'rate_limit': config('RDSTATION_RATE_LIMIT', default=180, cast=int),  # req/min
    'rate_limit_per_second': config('RDSTATION_RATE_LIMIT_PER_SECOND', default=3, cast=int),
    'timeout': config('RDSTATION_TIMEOUT', default=30, cast=int),
    'retry_attempts': config('RDSTATION_RETRY_ATTEMPTS', default=3, cast=int),
    'retry_delay': config('RDSTATION_RETRY_DELAY', default=1, cast=int),
    'page_size': config('RDSTATION_PAGE_SIZE', default=200, cast=int),
    'max_pages': config('RDSTATION_MAX_PAGES', default=1000, cast=int),
    # Configurações para contornar limite de 10.000 registros da API
    'max_offset_limit': config('RDSTATION_MAX_OFFSET_LIMIT', default=10000, cast=int), 
    # ESTRATÉGIA OTIMIZADA: Extrai apenas os registros mais recentes (ordenação decrescente por ID)
    # Esta abordagem é mais eficiente e garante que sempre temos os dados mais atualizados
}

# =============================================================================
# DATA EXTENSIONS (TABELAS DESTINO)
# =============================================================================

DATA_EXTENSIONS = {
    'tb_clientes': {
        'external_key': 'B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E',
        'required_fields': ['cnpjcpf', 'email'],
        'estimated_records': 73498,
        'batch_count': 37,  # 73498 / 2000
        'priority': 2,
    },
    'tb_leads': {
        'external_key': 'EC0B7BFF-EC89-4A4D-914B-749F14B6F861',
        'required_fields': ['cnpjcpf', 'dt_simulacao'],
        'estimated_records': 0,  # Variable
        'batch_count': 0,  # Dynamic
        'priority': 3,
    },
    'tb_produtos': {
        'external_key': 'FCC1DCA7-D286-458D-BDCC-D050C1BA61A8',
        'required_fields': ['id_produto'],
        'estimated_records': 20502,
        'batch_count': 11,  # 20502 / 2000
        'priority': 1,
    },
    'tb_propostas': {
        'external_key': '36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA',
        'required_fields': ['idproposta', 'email'],
        'estimated_records': 533238,
        'batch_count': 267,  # 533238 / 2000
        'priority': 4,
    }
}

# =============================================================================
# CONFIGURAÇÕES DE PROCESSAMENTO
# =============================================================================

BATCH_SIZES = {
    'salesforce_upload': config('BATCH_SIZE_SALESFORCE', default=2000, cast=int),
    'database_query': config('BATCH_SIZE_DATABASE', default=10000, cast=int),
    'rdstation_api': config('BATCH_SIZE_RDSTATION', default=200, cast=int),
    'memory_limit': config('BATCH_SIZE_MEMORY', default=50000, cast=int),
}

RETRY_CONFIGS = {
    'max_attempts': config('RETRY_MAX_ATTEMPTS', default=3, cast=int),
    'base_delay': config('RETRY_BASE_DELAY', default=1, cast=int),
    'max_delay': config('RETRY_MAX_DELAY', default=60, cast=int),
    'exponential_base': config('RETRY_EXPONENTIAL_BASE', default=2, cast=int),
    'jitter': config('RETRY_JITTER', default=True, cast=bool),
}

TIMEOUT_CONFIGS = {
    'database_query': config('TIMEOUT_DATABASE_QUERY', default=300, cast=int),
    'api_request': config('TIMEOUT_API_REQUEST', default=60, cast=int),
    'file_operation': config('TIMEOUT_FILE_OPERATION', default=30, cast=int),
    'total_pipeline': config('TIMEOUT_TOTAL_PIPELINE', default=7200, cast=int),  # 2 hours
}

# =============================================================================
# CONFIGURAÇÕES DE LOGGING
# =============================================================================

LOGGING_CONFIG = {
    'level': config('LOG_LEVEL', default='INFO'),
    'format': config('LOG_FORMAT', default='%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
    'file_path': config('LOG_FILE_PATH', default='logs/etl_consolidated.log'),
    'max_file_size': config('LOG_MAX_FILE_SIZE', default=10485760, cast=int),  # 10MB
    'backup_count': config('LOG_BACKUP_COUNT', default=5, cast=int),
    'console_output': config('LOG_CONSOLE_OUTPUT', default=True, cast=bool),
    'json_format': config('LOG_JSON_FORMAT', default=False, cast=bool),
}

# =============================================================================
# CONFIGURAÇÕES DE MONITORAMENTO
# =============================================================================

MONITORING_CONFIG = {
    'enable_progress_bar': config('MONITORING_PROGRESS_BAR', default=True, cast=bool),
    'enable_metrics': config('MONITORING_METRICS', default=True, cast=bool),
    'metrics_interval': config('MONITORING_METRICS_INTERVAL', default=10, cast=int),
    'enable_alerts': config('MONITORING_ALERTS', default=True, cast=bool),
    'alert_email': config('MONITORING_ALERT_EMAIL', default=''),
    'webhook_url': config('MONITORING_WEBHOOK_URL', default=''),
}

# =============================================================================
# CONFIGURAÇÕES DE AMBIENTE
# =============================================================================

ENVIRONMENT_CONFIG = {
    'env': config('ENVIRONMENT', default='production'),
    'debug': config('DEBUG', default=False, cast=bool),
    'dry_run': config('DRY_RUN', default=False, cast=bool),
    'parallel_execution': config('PARALLEL_EXECUTION', default=True, cast=bool),
    'max_workers': config('MAX_WORKERS', default=4, cast=int),
    'memory_limit_mb': config('MEMORY_LIMIT_MB', default=2048, cast=int),
}

# =============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# =============================================================================

SECURITY_CONFIG = {
    'mask_sensitive_data': config('SECURITY_MASK_SENSITIVE_DATA', default=True, cast=bool),
    'log_sensitive_data': config('SECURITY_LOG_SENSITIVE_DATA', default=False, cast=bool),
    'encrypt_credentials': config('SECURITY_ENCRYPT_CREDENTIALS', default=True, cast=bool),
    'audit_trail': config('SECURITY_AUDIT_TRAIL', default=True, cast=bool),
}

# =============================================================================
# MAPEAMENTO DE SEQUÊNCIA DE EXECUÇÃO
# =============================================================================

EXECUTION_SEQUENCE = [
    'tb_produtos',    # Prioridade 1
    'tb_clientes',    # Prioridade 2
    'tb_leads',       # Prioridade 3
    'tb_propostas',   # Prioridade 4
]

# =============================================================================
# FUNÇÕES AUXILIARES
# =============================================================================

def get_database_config(database_name: str) -> Dict[str, Any]:
    """Retorna configuração de banco de dados específico"""
    return DATABASE_CONFIGS.get(database_name, {})

def get_data_extension_config(table_name: str) -> Dict[str, Any]:
    """Retorna configuração de Data Extension específica"""
    return DATA_EXTENSIONS.get(table_name, {})

def get_batch_size(context: str) -> int:
    """Retorna tamanho do lote para contexto específico"""
    return BATCH_SIZES.get(context, 1000)

def get_retry_config() -> Dict[str, Any]:
    """Retorna configuração de retry"""
    return RETRY_CONFIGS

def is_dry_run() -> bool:
    """Verifica se está em modo dry-run"""
    return ENVIRONMENT_CONFIG['dry_run']

def is_debug() -> bool:
    """Verifica se está em modo debug"""
    return ENVIRONMENT_CONFIG['debug']

def get_execution_order() -> list:
    """Retorna ordem de execução das tabelas"""
    return EXECUTION_SEQUENCE

def validate_required_env_vars() -> tuple:
    """Valida se todas as variáveis de ambiente obrigatórias estão definidas"""
    required_vars = [
        'NEWCON_USER',
        'NEWCON_PASSWORD',
        'DW_USER',
        'DW_PASSWORD',
        'ORBBITS_DATABASE',
        'ORBBITS_USER',
        'ORBBITS_PASSWORD',
        'SALESFORCE_CLIENT_SECRET',
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    is_valid = len(missing_vars) == 0
    return is_valid, missing_vars

# =============================================================================
# VALIDAÇÃO DE CONFIGURAÇÕES
# =============================================================================

def validate_configuration() -> None:
    """Valida todas as configurações na inicialização"""
    # TODO: AGENTE_2 implementar validação completa
    # TODO: AGENTE_3 adicionar validações específicas para transformações
    # TODO: AGENTE_4 adicionar validações específicas para carregamento
    is_valid, missing_vars = validate_required_env_vars()
    
    if not is_valid:
        raise ValueError(f"Variáveis de ambiente obrigatórias não encontradas: {', '.join(missing_vars)}")
    
    # Validações básicas
    if SALESFORCE_CONFIG['batch_size'] > 2000:
        raise ValueError("Salesforce batch_size não pode ser maior que 2000")
    
    if RDSTATION_CONFIG['rate_limit'] > 180:
        raise ValueError("RD Station rate_limit não pode ser maior que 180 req/min")

if __name__ == "__main__":
    # Testa configurações
    try:
        validate_configuration()
        print("✅ Todas as configurações são válidas!")
    except Exception as e:
        print(f"❌ Erro nas configurações: {e}")