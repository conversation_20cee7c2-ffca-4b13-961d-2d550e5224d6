#!/bin/bash

# Script para fazer downgrade do Airflow 2.9.1 -> 2.8.1 sem perder dados

echo "=== Fazendo backup do banco antes do downgrade ==="
kubectl exec -n airflow deployment/airflow-postgresql -- pg_dump -U postgres airflow > airflow_backup_$(date +%Y%m%d_%H%M%S).sql

echo "=== Deletando job de migração atual ==="
kubectl delete job airflow-run-airflow-migrations -n airflow --ignore-not-found=true

echo "=== Fazendo downgrade das migrações para Airflow 2.8.1 ==="
# Primeiro, criar um pod temporário com Airflow 2.9.1 para fazer o downgrade
kubectl run airflow-downgrade --rm -i --restart=Never --image=apache/airflow:2.9.1 -n airflow -- \
  airflow db downgrade --to-revision 7b2661a43ba3

echo "=== Rebuilding imagem Docker com Airflow 2.8.1 ==="
docker build -t airflow-custom:2.8.1 .

echo "=== Reinstalando Helm chart com nova versão ==="
helm upgrade --install airflow apache-airflow/airflow \
  --namespace airflow \
  -f values.yaml \
  --version 1.16.0 \
  --set images.airflow.repository=airflow-custom \
  --set images.airflow.tag=2.8.1

echo "=== Aguardando inicialização dos pods ==="
kubectl wait --for=condition=ready pod -l component=scheduler -n airflow --timeout=300s

echo "=== Verificando logs de migração ==="
kubectl logs -l job-name=airflow-run-airflow-migrations -n airflow