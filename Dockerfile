FROM apache/airflow:2.8.1

# Copy requirements.txt
COPY requirements.txt .

USER root

# Instala pacotes do sistema
RUN apt-get update && \
    apt-get install -y \
    build-essential \
    python3-dev \
    freetds-dev \
    freetds-bin \
    gnupg \
    libkrb5-dev \
    libssl-dev \
    libgssapi-krb5-2 \
    wget \
    unzip \
    xvfb \
    libxi6 \
    libgconf-2-4 \
    libnss3 \
    libgbm1 \
    libasound2 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libxss1 \
    libxtst6 \
    libaio1 \
    default-jdk

# Instala Chrome
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google.list && \
    apt-get update && \
    apt-get install -y google-chrome-stable

# Instala ODBC
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    curl https://packages.microsoft.com/config/debian/10/prod.list > /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql17

# Oracle client
RUN mkdir -p /opt/oracle && \
    wget https://download.oracle.com/otn_software/linux/instantclient/19800/instantclient-basic-linux.x64-********.0dbru.zip -P /tmp && \
    wget https://download.oracle.com/otn_software/linux/instantclient/19800/instantclient-sdk-linux.x64-********.0dbru.zip -P /tmp && \
    unzip /tmp/instantclient-basic-linux.x64-********.0dbru.zip -d /opt/oracle && \
    unzip /tmp/instantclient-sdk-linux.x64-********.0dbru.zip -d /opt/oracle && \
    rm /tmp/*.zip

# ⚠️ Limpeza segura (sem remover /var/lib/apt/lists inteiramente)
RUN apt-get clean && rm -rf /tmp/*

# Variáveis de ambiente
ENV ODBCINI=/etc/odbc.ini
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_19_8
ENV PYTHONUNBUFFERED=1
ENV DISPLAY=:99

# Volta para usuário airflow
USER airflow

# Apenas instale pacotes
# Install Python packages
RUN pip install --upgrade pip && \
    pip install --no-cache-dir cython setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt
