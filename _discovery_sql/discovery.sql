  
CREATE PROCEDURE dbo.pr_MapaProducaoSeguro  @DT_Periodo_Vendas_Inicial         Datetime,  
                                                @DT_Periodo_Vendas_Final           Datetime,  
                                                @CD_Grupo_Inicial                  Char(6),  
                                                @CD_Grupo_Final                    Char(6),  
            @Com_Seguro CHAR(1)   
AS BEGIN  
  
  DECLARE @DT_Base         Datetime,  
          @ST_Utilizar_Bem Char(1);  --// 'A' Atual da cota 'V' Na venda da cota  
  
  SELECT @DT_Base         = dbo.fn_GetDate(GETDATE()),  
         @ST_Utilizar_Bem = 'A';  
  
  DECLARE @Comissionado_TMP TABLE(ROWID            Integer Identity(1,1) PRIMARY KEY,  
                                  ID_Comissionado  Integer,  
                                  CD_Comissionado  Char(6) COLLATE Latin1_General_CI_AS,  
                                  NM_Comissionado  VarChar(80) COLLATE Latin1_General_CI_AS);  
  
  DECLARE @Rel_TMP TABLE(ROWID                    Integer Identity(1,1) PRIMARY KEY,  
                         CD_Grupo                 Char(6) COLLATE Latin1_General_CI_AS,  
                         CD_Co<PERSON>int,  
                         Versao                   <PERSON>int,  
                         NM_Pessoa                VarChar(80) COLLATE Latin1_General_CI_AS,  
                         CD_Regiao_Fiscal         Char(6) COLLATE Latin1_General_CI_AS,  
                         ID_Regiao_Fiscal         Integer,  
                         ID_Cota                  Integer,  
                         ID_Bem                   Integer,  
                         CD_Bem                   Char(6) COLLATE Latin1_General_CI_AS,  
                         NM_Bem                   VarChar(50) COLLATE Latin1_General_CI_AS,  
                         VL_Bem                   Numeric(18,2),  
                         PE_Taxa_Administracao    Numeric(8,4),  
                         PZ_Cota                  Smallint,  
                         CD_Comissionado          Char(6) COLLATE Latin1_General_CI_AS,  
                         NM_Comissionado          VarChar(80) COLLATE Latin1_General_CI_AS,  
                         DT_Venda                 DateTime);  
  
  INSERT INTO @Comissionado_TMP(ID_Comissionado,  
                                CD_Comissionado,  
            NM_Comissionado)  
  SELECT DISTINCT  
         CONVE014.ID_Comissionado,  
         CONVE014.CD_Comissionado,  
         CORCC023.NM_Pessoa  
  FROM CONVE014 (NOLOCK)  
       INNER JOIN CONVE015 (NOLOCK)  
       ON CONVE015.ID_Comissionado = CONVE014.ID_Comissionado  
       INNER JOIN CONVE026 (NOLOCK)  
       ON CONVE026.ID_Comissionado = CONVE014.ID_Comissionado  
       INNER JOIN CONVE008(NOLOCK)  
       ON CONVE026.ID_Categoria_Comissionado = CONVE008.ID_Categoria_Comissionado  
       INNER JOIN CONVE016 (NOLOCK)  
       ON CONVE016.ID_Comissionado = CONVE014.ID_Comissionado  
       INNER JOIN CONVE009 (NOLOCK)  
       ON CONVE009.ID_Equipe_Venda = CONVE016.ID_Equipe_Venda  
       INNER JOIN CONVE001 (NOLOCK)  
       ON CONVE001.ID_Ponto_Venda = CONVE016.ID_Ponto_Venda  
       INNER JOIN CONVE003 (NOLOCK)  
       ON CONVE003.ID_Tipo_Ponto_Venda = CONVE001.ID_Tipo_Ponto_Venda  
       INNER JOIN CORCC023 (NOLOCK)  
       ON CONVE014.ID_Pessoa = CORCC023.ID_Pessoa  
  --WHERE CONVE014.CD_Comissionado           BETWEEN @CD_Comissionado_Inicial           AND @CD_Comissionado_Final  
  --  --AND @DT_Base                           BETWEEN CONVE015.DT_IV_CONVE015            AND CONVE015.DT_FV_CONVE015--conveniado ativo ou desativado  
  --  AND CONVE008.CD_Categoria_Comissionado BETWEEN @CD_Categoria_Comissionado_Inicial AND @CD_Categoria_Comissionado_Final  
  --  AND CONVE009.CD_Equipe_Venda           BETWEEN @CD_Equipe_Venda_Inicial           AND @CD_Equipe_Venda_Final  
  --  AND CONVE003.CD_Tipo_Ponto_Venda       BETWEEN @CD_Tipo_Ponto_Venda_Inicial       AND @CD_Tipo_Ponto_Venda_Final;  
  
  IF (@ST_Utilizar_Bem = 'A')  
BEGIN  
    WITH CTE (ID_Cota,  
              DT_Contabilizacao) AS (SELECT CONFI005C.ID_Cota,  
                                            MIN(CONFI005.DT_Contabilizacao) AS DT_Contabilizacao  
                                     FROM CONFI005C(NOLOCK)  
                                          INNER JOIN CONFI005 WITH(NOLOCK)  
                                          ON CONFI005.ID_Movimento_Grupo = CONFI005C.ID_Movimento_Grupo  
                                     WHERE CONFI005.ID_Movimento_Estorno = 0  
                                     GROUP BY CONFI005C.ID_Cota)  
    INSERT INTO @Rel_TMP(CD_Grupo              ,  
                         CD_Cota               ,  
                         Versao                ,  
                         ID_Cota               ,  
                         NM_Pessoa             ,  
                         CD_Regiao_Fiscal      ,  
                         ID_Regiao_Fiscal      ,  
                         PE_Taxa_Administracao ,  
                         PZ_Cota               ,  
                         CD_Comissionado       ,  
                         NM_Comissionado       ,  
                         DT_Venda              ,  
                         ID_Bem                )  
    SELECT CONGR001.CD_Grupo,  
           CONVE002.CD_Cota,  
           CONVE002.Versao,  
           CONVE002.ID_Cota,  
           CORCC023.NM_Pessoa,  
           fn_rsVeRegiaoFiscalCota.CD_Regiao_Fiscal,  
           fn_rsVeRegiaoFiscalCota.ID_Regiao_Fiscal,  
           CONVE002A.PE_TA_Plano,  
           CONVE002.PZ_Cota,  
           Comissionado_TMP.CD_Comissionado,  
           Comissionado_TMP.NM_Comissionado,  
           CONVE002D.DT_Venda,  
           CONVE024.ID_Bem  
    FROM CONVE002 (NOLOCK)  
         INNER JOIN CONGR001 (NOLOCK)  
         ON CONVE002.ID_Grupo = CONGR001.ID_Grupo  
         INNER JOIN CONVE002D (NOLOCK)  
         ON CONVE002.ID_Cota = CONVE002D.ID_Cota  
         INNER JOIN CONVE002A (NOLOCK)  
         ON CONVE002.ID_Cota = CONVE002A.ID_Cota  
         INNER JOIN vw_VeDTContemplacao  
         ON CONVE002.ID_Cota = vw_VeDTContemplacao.ID_Cota  
         INNER JOIN @Comissionado_TMP Comissionado_TMP  
         ON CONVE002.ID_Comissionado = Comissionado_TMP.ID_Comissionado  
         INNER JOIN dbo.fn_rsVeRegiaoFiscalCota() fn_rsVeRegiaoFiscalCota  
         ON CONVE002.ID_Cota = fn_rsVeRegiaoFiscalCota.ID_Cota  
   INNER JOIN CORCC023 (NOLOCK)  
         ON CONVE002.ID_Pessoa = CORCC023.ID_Pessoa  
         INNER JOIN CONCC002 (NOLOCK)  
         ON CONCC002.ID_Filial = CONVE002D.ID_Filial_Adm  
         INNER JOIN CONVE024 (NOLOCK)  
         ON CONVE002.ID_Cota = CONVE024.ID_Cota  
         INNER JOIN CONBE007 (NOLOCK)  
         ON CONVE024.ID_Bem = CONBE007.ID_Bem  
         LEFT JOIN CONBE007A (NOLOCK)  
         ON CONBE007.ID_Bem = CONBE007A.ID_Bem  
         LEFT JOIN CONBE005 (NOLOCK)  
         ON CONBE005.ID_Fabricante = CONBE007A.ID_Fabricante   
    WHERE CONGR001.CD_Grupo                         BETWEEN @CD_Grupo_Inicial          AND @CD_Grupo_Final  
      --AND CONVE002.Versao                           BETWEEN @Versao_Inicial            AND @Versao_Final  
      --AND fn_rsVeRegiaoFiscalCota.CD_Regiao_Fiscal  BETWEEN @CD_Regiao_Fiscal_Inicial  AND @CD_Regiao_Fiscal_Final  
      --AND CONCC002.CD_Filial                        BETWEEN @CD_Filial_Adm_Inicial     AND @CD_Filial_Adm_Final  
      --AND vw_VeDTContemplacao.ST_Contemplacao       BETWEEN @ST_Contemplacao_Inicial   AND @ST_Contemplacao_Final  
      AND EXISTS(SELECT 1 FROM CTE  
                 WHERE CTE.ID_Cota           = CONVE002.ID_Cota  
                   AND CTE.DT_Contabilizacao BETWEEN @DT_Periodo_Vendas_Inicial AND @DT_Periodo_Vendas_Final)  
      AND CONVE024.ID_CONVE024 = (SELECT MAX(B.ID_CONVE024)  
                                  FROM CONVE024 B (NOLOCK)  
                                  WHERE B.ID_Cota = CONVE002.ID_Cota)  
    ORDER BY fn_rsVeRegiaoFiscalCota.CD_Regiao_Fiscal,  
             CONGR001.CD_Grupo,  
             CONVE002.CD_Cota,  
             CONVE002.Versao;  
  END  
  ELSE  
  BEGIN  
    WITH CTE (ID_Cota,  
              DT_Contabilizacao) AS (SELECT CONFI005C.ID_Cota,  
                                            MIN(CONFI005.DT_Contabilizacao) AS DT_Contabilizacao  
                                     FROM CONFI005C(NOLOCK)  
                                          INNER JOIN CONFI005 WITH(NOLOCK)  
                                          ON CONFI005.ID_Movimento_Grupo = CONFI005C.ID_Movimento_Grupo  
                                     WHERE CONFI005.ID_Movimento_Estorno = 0  
                                     GROUP BY CONFI005C.ID_Cota)  
    INSERT INTO @Rel_TMP(CD_Grupo              ,  
                         CD_Cota               ,  
                         Versao                ,  
                         ID_Cota               ,  
                         NM_Pessoa             ,  
                         CD_Regiao_Fiscal      ,  
                         ID_Regiao_Fiscal      ,  
                         PE_Taxa_Administracao ,  
                         PZ_Cota               ,  
                         CD_Comissionado       ,  
                         NM_Comissionado       ,  
                         DT_Venda              ,  
                         ID_Bem                )  
    SELECT CONGR001.CD_Grupo,  
           CONVE002.CD_Cota,  
           CONVE002.Versao,  
           CONVE002.ID_Cota,  
           CORCC023.NM_Pessoa,  
           fn_rsVeRegiaoFiscalCota.CD_Regiao_Fiscal,  
           fn_rsVeRegiaoFiscalCota.ID_Regiao_Fiscal,  
           CONVE002A.PE_TA_Plano,  
           CONVE002.PZ_Cota,  
           Comissionado_TMP.CD_Comissionado,  
           Comissionado_TMP.NM_Comissionado,  
           CONVE002D.DT_Venda,  
           CONVE024.ID_Bem  
    FROM CONVE002 (NOLOCK)  
         INNER JOIN CONGR001 (NOLOCK)  
         ON CONVE002.ID_Grupo = CONGR001.ID_Grupo  
         INNER JOIN CONVE002D (NOLOCK)  
         ON CONVE002.ID_Cota = CONVE002D.ID_Cota  
         INNER JOIN CONVE002A (NOLOCK)  
         ON CONVE002.ID_Cota = CONVE002A.ID_Cota  
         INNER JOIN vw_VeDTContemplacao  
         ON CONVE002.ID_Cota = vw_VeDTContemplacao.ID_Cota  
         INNER JOIN @Comissionado_TMP Comissionado_TMP  
         ON CONVE002.ID_Comissionado = Comissionado_TMP.ID_Comissionado  
         INNER JOIN dbo.fn_rsVeRegiaoFiscalCota() fn_rsVeRegiaoFiscalCota  
         ON CONVE002.ID_Cota = fn_rsVeRegiaoFiscalCota.ID_Cota  
         INNER JOIN CORCC023 (NOLOCK)  
         ON CONVE002.ID_Pessoa = CORCC023.ID_Pessoa  
         INNER JOIN CONCC002 (NOLOCK)  
         ON CONCC002.ID_Filial = CONVE002D.ID_Filial_Adm  
         INNER JOIN CONVE024 (NOLOCK)  
         ON CONVE002.ID_Cota = CONVE024.ID_Cota  
         INNER JOIN CONBE007 (NOLOCK)  
         ON CONVE024.ID_Bem = CONBE007.ID_Bem  
         LEFT JOIN CONBE007A (NOLOCK)  
         ON CONBE007.ID_Bem = CONBE007A.ID_Bem  
         LEFT JOIN CONBE005 (NOLOCK)  
         ON CONBE005.ID_Fabricante = CONBE007A.ID_Fabricante   
              
    WHERE CONGR001.CD_Grupo                         BETWEEN @CD_Grupo_Inicial          AND @CD_Grupo_Final  
      --AND CONVE002.Versao                           BETWEEN @Versao_Inicial            AND @Versao_Final  
      --AND fn_rsVeRegiaoFiscalCota.CD_Regiao_Fiscal  BETWEEN @CD_Regiao_Fiscal_Inicial  AND @CD_Regiao_Fiscal_Final  
      --AND CONCC002.CD_Filial                        BETWEEN @CD_Filial_Adm_Inicial     AND @CD_Filial_Adm_Final  
      --AND vw_VeDTContemplacao.ST_Contemplacao       BETWEEN @ST_Contemplacao_Inicial   AND @ST_Contemplacao_Final  
      AND EXISTS(SELECT 1 FROM CTE  
                 WHERE CTE.ID_Cota           = CONVE002.ID_Cota  
                   AND CTE.DT_Contabilizacao BETWEEN @DT_Periodo_Vendas_Inicial AND @DT_Periodo_Vendas_Final)  
      AND CONVE024.ID_CONVE024 = (SELECT MIN(B.ID_CONVE024)  
                                  FROM CONVE024 B (NOLOCK)  
                                  WHERE B.ID_Cota = CONVE002.ID_Cota)  
    ORDER BY fn_rsVeRegiaoFiscalCota.CD_Regiao_Fiscal,  
             CONGR001.CD_Grupo,  
             CONVE002.CD_Cota,  
             CONVE002.Versao;  
  END  
  
  UPDATE @Rel_TMP  
  SET Rel_TMP.VL_Bem = A.VL_Total  
  FROM @Rel_TMP Rel_TMP  
  CROSS APPLY dbo.fn_dsBeVlBem(Rel_TMP.ID_Bem,  
                               Rel_TMP.ID_Regiao_Fiscal,  
                               @DT_Base) A;  
  
  UPDATE @Rel_TMP  
  SET Rel_TMP.CD_Bem = CONBE007.CD_Bem,  
      Rel_TMP.NM_Bem = CONBE007.NM_Bem  
  FROM @Rel_TMP Rel_TMP  
  INNER JOIN CONBE007  
  ON CONBE007.ID_Bem = Rel_TMP.ID_Bem;  
  
  SELECT CD_Grupo             ,  
         CD_Cota              ,  
         Versao               ,  
         NM_Pessoa            ,  
         CD_Regiao_Fiscal     ,  
         CD_Bem               ,  
         NM_Bem               ,  
         VL_Bem               ,  
         PE_Taxa_Administracao,  
         PZ_Cota              ,  
         CD_Comissionado      ,  
         NM_Comissionado      ,  
         DT_Venda,  
   CASE CONPV004.SN_Optante  
   WHEN 'S' THEN 'Sim'  
   WHEN 'N' THEN 'Não'  
  END AS Segurado,  
  CAST(VL_Bem * 0.00036 AS decimal(10,2)) AS ValorSeguroParcela  
  FROM @Rel_TMP A  
  INNER JOIN  
  CONCC036   
 ON  
  A.ID_COTA = CONCC036.ID_COTA  
 INNER JOIN  
 CONPV004  
ON  
 CONCC036.id_documento = CONPV004.ID_Documento   
WHERE  
 CONPV004.ID_Tipo_Seguro = 1   
 AND CONPV004.SN_Optante = @Com_Seguro   
  ORDER BY CD_Regiao_Fiscal,  
           CD_Bem,  
           CD_Grupo,  
           CD_Cota,  
           Versao;  
  
END